{"groups": [{"name": "ollama", "type": "org.dromara.app.config.OllamaConfig$OllamaProperties", "sourceType": "org.dromara.app.config.OllamaConfig$OllamaProperties"}, {"name": "ollama.system-prompts", "type": "org.dromara.app.config.OllamaConfig$OllamaProperties$SystemPrompts", "sourceType": "org.dromara.app.config.OllamaConfig$OllamaProperties", "sourceMethod": "public org.dromara.app.config.OllamaConfig.OllamaProperties.SystemPrompts getSystemPrompts() "}], "properties": [{"name": "ollama.base-url", "type": "java.lang.String", "description": "Ollama服务器地址", "sourceType": "org.dromara.app.config.OllamaConfig$OllamaProperties"}, {"name": "ollama.connect-timeout", "type": "java.lang.Integer", "description": "连接超时时间（秒）", "sourceType": "org.dromara.app.config.OllamaConfig$OllamaProperties"}, {"name": "ollama.default-max-tokens", "type": "java.lang.Integer", "description": "默认最大Token数", "sourceType": "org.dromara.app.config.OllamaConfig$OllamaProperties"}, {"name": "ollama.default-model", "type": "java.lang.String", "description": "默认模型名称", "sourceType": "org.dromara.app.config.OllamaConfig$OllamaProperties"}, {"name": "ollama.default-temperature", "type": "java.lang.Double", "description": "默认温度参数", "sourceType": "org.dromara.app.config.OllamaConfig$OllamaProperties"}, {"name": "ollama.embedding-model", "type": "java.lang.String", "description": "嵌入模型名称", "sourceType": "org.dromara.app.config.OllamaConfig$OllamaProperties"}, {"name": "ollama.enable-health-check", "type": "java.lang.Bo<PERSON>an", "description": "是否启用健康检查", "sourceType": "org.dromara.app.config.OllamaConfig$OllamaProperties"}, {"name": "ollama.enable-streaming", "type": "java.lang.Bo<PERSON>an", "description": "是否启用流式响应", "sourceType": "org.dromara.app.config.OllamaConfig$OllamaProperties"}, {"name": "ollama.health-check-interval", "type": "java.lang.Integer", "description": "健康检查间隔（秒）", "sourceType": "org.dromara.app.config.OllamaConfig$OllamaProperties"}, {"name": "ollama.read-timeout", "type": "java.lang.Integer", "description": "读取超时时间（秒）", "sourceType": "org.dromara.app.config.OllamaConfig$OllamaProperties"}, {"name": "ollama.retry-count", "type": "java.lang.Integer", "description": "重试次数", "sourceType": "org.dromara.app.config.OllamaConfig$OllamaProperties"}, {"name": "ollama.retry-interval", "type": "java.lang.Long", "description": "重试间隔（毫秒）", "sourceType": "org.dromara.app.config.OllamaConfig$OllamaProperties"}, {"name": "ollama.similarity-threshold", "type": "java.lang.Double", "description": "相似度阈值", "sourceType": "org.dromara.app.config.OllamaConfig$OllamaProperties"}, {"name": "ollama.system-prompts.career-advisor", "type": "java.lang.String", "sourceType": "org.dromara.app.config.OllamaConfig$OllamaProperties$SystemPrompts"}, {"name": "ollama.system-prompts.general", "type": "java.lang.String", "sourceType": "org.dromara.app.config.OllamaConfig$OllamaProperties$SystemPrompts"}, {"name": "ollama.system-prompts.interviewer", "type": "java.lang.String", "sourceType": "org.dromara.app.config.OllamaConfig$OllamaProperties$SystemPrompts"}, {"name": "ollama.system-prompts.learning-guide", "type": "java.lang.String", "sourceType": "org.dromara.app.config.OllamaConfig$OllamaProperties$SystemPrompts"}, {"name": "ollama.system-prompts.mock-interviewer", "type": "java.lang.String", "sourceType": "org.dromara.app.config.OllamaConfig$OllamaProperties$SystemPrompts"}, {"name": "ollama.system-prompts.resume-analyzer", "type": "java.lang.String", "sourceType": "org.dromara.app.config.OllamaConfig$OllamaProperties$SystemPrompts"}, {"name": "ollama.system-prompts.skill-assessor", "type": "java.lang.String", "sourceType": "org.dromara.app.config.OllamaConfig$OllamaProperties$SystemPrompts"}, {"name": "ollama.vector-dimension", "type": "java.lang.Integer", "description": "向量维度", "sourceType": "org.dromara.app.config.OllamaConfig$OllamaProperties"}, {"name": "ollama.write-timeout", "type": "java.lang.Integer", "description": "写入超时时间（秒）", "sourceType": "org.dromara.app.config.OllamaConfig$OllamaProperties"}], "hints": []}