{"doc": "\n 认证控制器\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "sendSmsCode", "paramTypes": ["java.util.Map"], "doc": "\n 发送手机验证码\r\n"}, {"name": "sendEmailCode", "paramTypes": ["java.util.Map"], "doc": "\n 发送邮箱验证码\r\n"}, {"name": "loginByPhone", "paramTypes": ["org.dromara.app.domain.dto.AppAuthDto"], "doc": "\n 手机号验证码登录\r\n"}, {"name": "loginByPassword", "paramTypes": ["org.dromara.app.domain.dto.AppAuthDto"], "doc": "\n 密码登录\r\n"}, {"name": "register", "paramTypes": ["org.dromara.app.domain.dto.AppAuthDto"], "doc": "\n 用户注册\r\n"}, {"name": "checkPhoneExists", "paramTypes": ["java.util.Map"], "doc": "\n 检查手机号是否已注册\r\n"}, {"name": "checkEmailExists", "paramTypes": ["java.util.Map"], "doc": "\n 检查邮箱是否已注册\r\n"}, {"name": "resetPassword", "paramTypes": ["org.dromara.app.domain.dto.AppAuthDto"], "doc": "\n 重置密码\r\n"}, {"name": "thirdParty<PERSON><PERSON>in", "paramTypes": ["org.dromara.app.domain.dto.AppAuthDto"], "doc": "\n 第三方登录 (基础实现，可根据需要扩展)\r\n"}, {"name": "refreshToken", "paramTypes": ["org.dromara.app.domain.dto.AppAuthDto"], "doc": "\n 刷新Token\r\n"}, {"name": "logout", "paramTypes": [], "doc": "\n 退出登录\r\n"}, {"name": "getCurrentUser", "paramTypes": [], "doc": "\n 获取当前用户信息\r\n"}], "constructors": []}