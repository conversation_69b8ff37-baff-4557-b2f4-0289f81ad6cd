{"doc": "\n 面试书籍控制器\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["java.lang.Integer", "java.lang.Integer", "java.lang.String", "java.lang.String"], "doc": "\n 分页查询书籍列表\r\n\r\n @param pageNum     页码\r\n @param pageSize    每页大小\r\n @param category    分类筛选\r\n @param searchQuery 搜索关键词\r\n @return 分页结果\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": "\n 查询书籍详情\r\n\r\n @param id 书籍ID\r\n @return 书籍详情\r\n"}, {"name": "getHotBooks", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询热门书籍列表\r\n\r\n @param limit 限制数量\r\n @return 热门书籍列表\r\n"}, {"name": "getRecommendedBooks", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询推荐书籍列表\r\n\r\n @param limit 限制数量\r\n @return 推荐书籍列表\r\n"}, {"name": "getCategoryStats", "paramTypes": [], "doc": "\n 查询分类统计信息\r\n\r\n @return 分类统计\r\n"}, {"name": "startReading", "paramTypes": ["java.lang.Long"], "doc": "\n 开始阅读书籍（增加阅读次数）\r\n\r\n @param id 书籍ID\r\n @return 操作结果\r\n"}, {"name": "getChapters", "paramTypes": ["java.lang.Long"], "doc": "\n 查询书籍章节列表\r\n\r\n @param bookId 书籍ID\r\n @return 章节列表\r\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>nt", "paramTypes": ["java.lang.String"], "doc": "\n 查询章节内容\r\n\r\n @param chapterId 章节ID\r\n @return 章节内容\r\n"}, {"name": "getChapterByOrder", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": "\n 根据书籍ID和章节序号查询章节\r\n\r\n @param bookId       书籍ID\r\n @param chapterOrder 章节序号\r\n @return 章节信息\r\n"}, {"name": "getPreviewChapters", "paramTypes": ["java.lang.Long"], "doc": "\n 查询试读章节列表\r\n\r\n @param bookId 书籍ID\r\n @return 试读章节列表\r\n"}, {"name": "add", "paramTypes": ["org.dromara.app.domain.Book"], "doc": "\n 新增书籍\r\n\r\n @param book 书籍信息\r\n @return 操作结果\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.app.domain.Book"], "doc": "\n 修改书籍\r\n\r\n @param book 书籍信息\r\n @return 操作结果\r\n"}, {"name": "remove", "paramTypes": ["java.util.List"], "doc": "\n 删除书籍\r\n\r\n @param ids 书籍ID列表\r\n @return 操作结果\r\n"}, {"name": "updateStatus", "paramTypes": ["java.lang.Long", "java.lang.Bo<PERSON>an"], "doc": "\n 更新书籍状态\r\n\r\n @param id     书籍ID\r\n @param status 状态\r\n @return 操作结果\r\n"}, {"name": "addChapter", "paramTypes": ["org.dromara.app.domain.BookChapter"], "doc": "\n 新增章节\r\n\r\n @param chapter 章节信息\r\n @return 操作结果\r\n"}, {"name": "edit<PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["org.dromara.app.domain.BookChapter"], "doc": "\n 修改章节\r\n\r\n @param chapter 章节信息\r\n @return 操作结果\r\n"}, {"name": "removeChapter", "paramTypes": ["java.lang.String"], "doc": "\n 删除章节\r\n\r\n @param chapterId 章节ID\r\n @return 操作结果\r\n"}, {"name": "updateChapterUnlock", "paramTypes": ["java.lang.String", "java.lang.Bo<PERSON>an"], "doc": "\n 更新章节解锁状态\r\n\r\n @param chapterId  章节ID\r\n @param isUnlocked 是否解锁\r\n @return 操作结果\r\n"}, {"name": "getReadingRecord", "paramTypes": ["java.lang.Long"], "doc": "\n 获取用户的阅读记录\r\n\r\n @param bookId 书籍ID\r\n @return 阅读记录\r\n"}, {"name": "saveReadingRecord", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.Integer", "java.lang.Integer", "java.util.Map"], "doc": "\n 保存或更新阅读记录\r\n\r\n @param bookId              书籍ID\r\n @param currentChapterId    当前章节ID\r\n @param currentChapterIndex 当前章节索引\r\n @param readingProgress     阅读进度\r\n @param readingSettings     阅读设置\r\n @return 操作结果\r\n"}, {"name": "mark<PERSON><PERSON><PERSON>erCompleted", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 标记章节已完成\r\n\r\n @param bookId    书籍ID\r\n @param chapterId 章节ID\r\n @return 操作结果\r\n"}, {"name": "getReadingHistory", "paramTypes": ["java.lang.Integer", "java.lang.Integer"], "doc": "\n 查询用户阅读历史\r\n\r\n @param pageNum  页码\r\n @param pageSize 每页大小\r\n @return 阅读历史分页\r\n"}, {"name": "getRecentReading", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询最近阅读的书籍\r\n\r\n @param limit 限制数量\r\n @return 最近阅读列表\r\n"}, {"name": "getReadingStats", "paramTypes": [], "doc": "\n 查询用户阅读统计\r\n\r\n @return 阅读统计数据\r\n"}, {"name": "addReadingTime", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": "\n 增加阅读时长\r\n\r\n @param bookId      书籍ID\r\n @param readingTime 阅读时长（分钟）\r\n @return 操作结果\r\n"}], "constructors": []}