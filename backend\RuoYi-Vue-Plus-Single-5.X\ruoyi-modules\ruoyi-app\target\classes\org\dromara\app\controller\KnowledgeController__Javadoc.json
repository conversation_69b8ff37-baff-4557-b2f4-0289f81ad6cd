{"doc": "\n 知识库管理Controller\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getKnowledgeBases", "paramTypes": [], "doc": "\n 获取知识库列表\r\n"}, {"name": "getKnowledgeBase", "paramTypes": ["java.lang.Long"], "doc": "\n 获取知识库详情\r\n"}, {"name": "createKnowledgeBase", "paramTypes": ["org.dromara.app.domain.KnowledgeBase"], "doc": "\n 创建知识库\r\n"}, {"name": "updateKnowledgeBase", "paramTypes": ["org.dromara.app.domain.KnowledgeBase"], "doc": "\n 更新知识库\r\n"}, {"name": "deleteKnowledgeBase", "paramTypes": ["java.lang.Long"], "doc": "\n 删除知识库\r\n"}, {"name": "getKnowledgeBaseStats", "paramTypes": ["java.lang.Long"], "doc": "\n 获取知识库统计信息\r\n"}, {"name": "rebuildIndex", "paramTypes": ["java.lang.Long"], "doc": "\n 重建知识库索引\r\n"}, {"name": "getDocuments", "paramTypes": ["java.lang.Long"], "doc": "\n 获取知识库文档列表\r\n"}, {"name": "addDocument", "paramTypes": ["org.dromara.app.domain.KnowledgeDocument"], "doc": "\n 添加文档到知识库\r\n"}, {"name": "deleteDocument", "paramTypes": ["java.lang.Long"], "doc": "\n 删除文档\r\n"}, {"name": "processDocument", "paramTypes": ["java.lang.Long"], "doc": "\n 处理文档（重新向量化）\r\n"}, {"name": "searchKnowledge", "paramTypes": ["org.dromara.app.controller.KnowledgeController.SearchRequest"], "doc": "\n 搜索知识库\r\n"}, {"name": "hybridSearch", "paramTypes": ["org.dromara.app.controller.KnowledgeController.SearchRequest"], "doc": "\n 混合搜索知识库\r\n"}], "constructors": []}