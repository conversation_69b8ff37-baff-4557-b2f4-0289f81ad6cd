{"doc": "\n 学习资源控制器\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getMajorList", "paramTypes": [], "doc": "\n 获取专业列表\r\n\r\n @return 专业列表响应\r\n"}, {"name": "getQuestionBankList", "paramTypes": ["org.dromara.app.domain.dto.QuestionBankQueryDto"], "doc": "\n 获取题库列表\r\n\r\n @param queryDto 查询参数\r\n @return 题库列表响应\r\n"}, {"name": "toggleBookmark", "paramTypes": ["java.util.Map"], "doc": "\n 切换题库收藏状态\r\n\r\n @param request 收藏请求参数\r\n @return 收藏状态响应\r\n"}, {"name": "searchQuestionBanks", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 搜索题库\r\n\r\n @param keyword 搜索关键词\r\n @param majorId 专业ID\r\n @return 题库列表响应\r\n"}, {"name": "getQuestionBankDetail", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 获取题库详情\r\n\r\n @param bankId  题库ID\r\n @param majorId 专业ID\r\n @return 题库详情响应\r\n"}, {"name": "getHotQuestionBanks", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取热门题库\r\n\r\n @param limit 限制数量\r\n @return 热门题库列表响应\r\n"}, {"name": "getNewQuestionBanks", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取最新题库\r\n\r\n @param limit 限制数量\r\n @return 最新题库列表响应\r\n"}, {"name": "getQuestionBankFullDetail", "paramTypes": ["java.lang.String"], "doc": "\n 获取题库完整详情（包含学习进度等信息）\r\n\r\n @param bankId 题库ID\r\n @return 题库完整详情响应\r\n"}, {"name": "getQuestionsByCategory", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Integer", "java.lang.Integer"], "doc": "\n 获取题库分类题目\r\n\r\n @param bankId 题库ID\r\n @return 按分类组织的题目列表响应\r\n"}, {"name": "getRecommendedQuestions", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": "\n 获取推荐题目\r\n\r\n @param bankId 题库ID\r\n @param limit  限制数量\r\n @return 推荐题目列表响应\r\n"}, {"name": "toggleQuestionBankBookmark", "paramTypes": ["java.lang.String"], "doc": "\n 切换题库收藏状态（新版）\r\n\r\n @param bankId 题库ID\r\n @return 收藏状态响应\r\n"}, {"name": "getQuestionList", "paramTypes": ["java.lang.String", "org.dromara.app.domain.dto.QuestionQueryDto"], "doc": "\n 获取题库题目列表（支持筛选和搜索）\r\n\r\n @param bankId   题库ID\r\n @param queryDto 查询参数\r\n @return 题目列表响应\r\n"}, {"name": "getQuestionStatistics", "paramTypes": ["java.lang.String"], "doc": "\n 获取题库题目统计信息\r\n\r\n @param bankId 题库ID\r\n @return 统计信息响应\r\n"}, {"name": "getQuestionDetail", "paramTypes": ["java.lang.String"], "doc": "\n 获取题目详情\r\n\r\n @param questionId 题目ID\r\n @return 题目详情响应\r\n"}, {"name": "searchQuestions", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String", "java.lang.Bo<PERSON>an"], "doc": "\n 搜索题目\r\n\r\n @param bankId     题库ID\r\n @param keyword    搜索关键词\r\n @param difficulty 难度等级\r\n @param category   分类\r\n @param completed  完成状态\r\n @return 题目列表响应\r\n"}, {"name": "toggleQuestionBookmark", "paramTypes": ["java.lang.String", "java.util.Map"], "doc": "\n 切换题目收藏状态\r\n\r\n @param questionId 题目ID\r\n @param request    收藏请求参数\r\n @return 收藏状态响应\r\n"}, {"name": "getQuestionComments", "paramTypes": ["java.lang.String", "java.lang.Integer", "java.lang.Integer", "java.lang.String", "java.lang.String"], "doc": "\n 获取题目评论列表\r\n\r\n @param questionId     题目ID\r\n @param page           页码\r\n @param pageSize       每页大小\r\n @param orderBy        排序字段\r\n @param orderDirection 排序方向\r\n @return 评论列表响应\r\n"}, {"name": "createQuestionComment", "paramTypes": ["java.lang.String", "java.util.Map"], "doc": "\n 创建题目评论\r\n\r\n @param questionId 题目ID\r\n @param request    评论创建请求\r\n @return 创建结果响应\r\n"}, {"name": "getMajorQuestionBankList", "paramTypes": ["java.lang.String", "org.dromara.app.domain.dto.QuestionBankQueryDto"], "doc": "\n 获取专业下的所有题库列表（增强版 - 支持更多筛选和排序）\r\n 专为 all-question-banks.vue 页面设计\r\n\r\n @param queryDto 查询参数\r\n @return 题库列表响应\r\n"}, {"name": "getMajorQuestionBankStatistics", "paramTypes": ["java.lang.String"], "doc": "\n 获取专业题库统计信息\r\n 专为 all-question-banks.vue 页面设计\r\n\r\n @param majorId 专业ID\r\n @return 统计信息响应\r\n"}, {"name": "getMajorQuestionBankFilterCounts", "paramTypes": ["java.lang.String"], "doc": "\n 获取专业题库筛选选项计数\r\n 专为 all-question-banks.vue 页面设计\r\n\r\n @param majorId 专业ID\r\n @return 筛选选项计数响应\r\n"}, {"name": "resetMajorQuestionBankFilters", "paramTypes": ["java.lang.String"], "doc": "\n 重置专业题库筛选条件\r\n 专为 all-question-banks.vue 页面设计\r\n\r\n @param majorId 专业ID\r\n @return 重置结果响应\r\n"}], "constructors": []}