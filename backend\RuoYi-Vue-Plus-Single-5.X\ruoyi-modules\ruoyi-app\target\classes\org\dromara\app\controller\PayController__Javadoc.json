{"doc": "\n 支付控制器\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "createPaymentOrder", "paramTypes": ["org.dromara.app.domain.dto.PaymentOrderDto", "jakarta.servlet.http.HttpServletRequest"], "doc": "\n 创建支付订单\r\n"}, {"name": "alipayPay", "paramTypes": ["java.lang.String", "java.lang.String", "jakarta.servlet.http.HttpServletResponse"], "doc": "\n 支付宝支付\r\n"}, {"name": "alipayNotify", "paramTypes": ["jakarta.servlet.http.HttpServletRequest"], "doc": "\n 支付宝异步通知\r\n"}, {"name": "alipayReturn", "paramTypes": ["jakarta.servlet.http.HttpServletRequest", "jakarta.servlet.http.HttpServletResponse"], "doc": "\n 支付宝同步回调\r\n"}, {"name": "queryOrderStatus", "paramTypes": ["java.lang.String"], "doc": "\n 查询订单状态\r\n"}, {"name": "cancelOrder", "paramTypes": ["java.lang.String"], "doc": "\n 取消订单\r\n"}, {"name": "getClientIP", "paramTypes": ["jakarta.servlet.http.HttpServletRequest"], "doc": "\n 获取客户端IP地址\r\n"}, {"name": "buildSuccessHtml", "paramTypes": [], "doc": "\n 构建成功页面HTML\r\n"}, {"name": "buildErrorHtml", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 构建错误页面HTML\r\n"}], "constructors": []}