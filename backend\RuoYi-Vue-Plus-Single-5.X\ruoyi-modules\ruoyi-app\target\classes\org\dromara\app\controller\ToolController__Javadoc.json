{"doc": "\n AI工具管理控制器\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getAvailableTools", "paramTypes": ["java.lang.String"], "doc": "\n 获取可用工具列表\r\n"}, {"name": "getToolDetail", "paramTypes": ["java.lang.String"], "doc": "\n 获取工具详情\r\n"}, {"name": "executeTool", "paramTypes": ["org.dromara.app.controller.ToolController.ToolExecuteRequest"], "doc": "\n 执行工具调用\r\n"}, {"name": "executeToolAsync", "paramTypes": ["org.dromara.app.controller.ToolController.ToolExecuteRequest"], "doc": "\n 异步执行工具调用\r\n"}, {"name": "executeBatchTools", "paramTypes": ["org.dromara.app.controller.ToolController.BatchToolExecuteRequest"], "doc": "\n 批量执行工具调用\r\n"}, {"name": "validateParameters", "paramTypes": ["org.dromara.app.controller.ToolController.ParameterValidationRequest"], "doc": "\n 验证工具调用参数\r\n"}, {"name": "getToolCallHistory", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 获取工具调用历史\r\n"}, {"name": "getSessionToolCalls", "paramTypes": ["java.lang.String"], "doc": "\n 获取会话工具调用记录\r\n"}, {"name": "getToolCallDetail", "paramTypes": ["java.lang.String"], "doc": "\n 获取工具调用详情\r\n"}, {"name": "retryToolCall", "paramTypes": ["java.lang.String"], "doc": "\n 重试工具调用\r\n"}, {"name": "getToolUsageStats", "paramTypes": [], "doc": "\n 获取工具使用统计\r\n"}, {"name": "getToolPerformanceStats", "paramTypes": ["java.lang.String"], "doc": "\n 获取工具性能统计\r\n"}, {"name": "registerTool", "paramTypes": ["org.dromara.app.domain.AiTool"], "doc": "\n 注册工具\r\n"}, {"name": "updateTool", "paramTypes": ["org.dromara.app.domain.AiTool"], "doc": "\n 更新工具\r\n"}, {"name": "deleteTool", "paramTypes": ["java.lang.String"], "doc": "\n 删除工具\r\n"}, {"name": "toggleTool", "paramTypes": ["java.lang.String", "java.lang.Bo<PERSON>an"], "doc": "\n 启用/禁用工具\r\n"}, {"name": "initSystemTools", "paramTypes": [], "doc": "\n 初始化系统工具\r\n"}], "constructors": []}