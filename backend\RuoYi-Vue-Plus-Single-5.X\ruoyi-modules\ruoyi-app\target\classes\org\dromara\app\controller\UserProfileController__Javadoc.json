{"doc": "\n 用户个人信息Controller\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "profile", "paramTypes": [], "doc": "\n 获取个人信息\r\n"}, {"name": "updateProfile", "paramTypes": ["org.dromara.app.domain.dto.AppUserProfileDto"], "doc": "\n 修改个人信息\r\n"}, {"name": "avatar", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": "\n 头像上传\r\n"}, {"name": "resetProfile", "paramTypes": [], "doc": "\n 重置个人信息\r\n"}], "constructors": []}