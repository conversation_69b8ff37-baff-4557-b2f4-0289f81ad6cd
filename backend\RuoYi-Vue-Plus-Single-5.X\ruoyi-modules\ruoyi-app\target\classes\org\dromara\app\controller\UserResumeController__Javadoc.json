{"doc": "\n 用户简历管理控制器\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.app.domain.bo.UserResumeBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询用户简历分页列表\r\n"}, {"name": "getMyResumeList", "paramTypes": [], "doc": "\n 查询当前用户的所有简历列表\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.String"], "doc": "\n 获取用户简历详细信息\r\n\r\n @param resumeId 简历主键\r\n"}, {"name": "uploadResume", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "java.lang.String", "java.lang.String"], "doc": "\n 上传用户简历文件\r\n\r\n @param file     简历文件\r\n @param fileName 原始文件名（可选）\r\n @param fileType 文件类型（可选）\r\n"}, {"name": "add", "paramTypes": ["org.dromara.app.domain.bo.UserResumeBo"], "doc": "\n 新增用户简历\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.app.domain.bo.UserResumeBo"], "doc": "\n 修改用户简历\r\n"}, {"name": "renameResume", "paramTypes": ["java.lang.Long", "java.util.Map"], "doc": "\n 重命名简历\r\n\r\n @param resumeId 简历ID\r\n @param request  重命名请求参数\r\n"}, {"name": "setDefaultResume", "paramTypes": ["java.lang.Long"], "doc": "\n 设置默认简历\r\n\r\n @param resumeId 简历ID\r\n"}, {"name": "cancelDefaultResume", "paramTypes": ["java.lang.Long"], "doc": "\n 取消默认简历\r\n\r\n @param resumeId 简历ID\r\n"}, {"name": "getDefaultResume", "paramTypes": [], "doc": "\n 获取当前用户的默认简历\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": "\n 删除用户简历\r\n\r\n @param resumeIds 简历主键串\r\n"}, {"name": "downloadResume", "paramTypes": ["java.lang.Long", "jakarta.servlet.http.HttpServletResponse"], "doc": "\n 下载用户简历文件\r\n\r\n @param resumeId 简历ID\r\n"}, {"name": "previewResumeContent", "paramTypes": ["java.lang.Long"], "doc": "\n 预览简历文件内容\r\n\r\n @param resumeId 简历ID\r\n @return 预览内容响应\r\n"}], "constructors": []}