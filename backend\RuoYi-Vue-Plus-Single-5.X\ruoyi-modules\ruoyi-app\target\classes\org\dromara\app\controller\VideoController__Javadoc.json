{"doc": "\n 视频课程控制器\r\n\r\n @Author: SevenJL\r\n @CreateTime: 2025-01-05\r\n @Version: 1.0\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.app.domain.bo.VideoQueryBo"], "doc": "\n 查询视频课程列表\r\n"}, {"name": "getHotVideos", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取热门视频\r\n"}, {"name": "getDetail", "paramTypes": ["java.lang.Long"], "doc": "\n 获取视频详情\r\n"}, {"name": "getLearningStats", "paramTypes": [], "doc": "\n 获取学习统计数据\r\n"}, {"name": "getBookmarkedVideos", "paramTypes": ["org.dromara.app.domain.bo.VideoQueryBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 获取收藏的视频\r\n"}, {"name": "getPurchasedVideos", "paramTypes": ["org.dromara.app.domain.bo.VideoQueryBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 获取已购买的视频\r\n"}, {"name": "getLearningHistory", "paramTypes": ["org.dromara.app.domain.bo.VideoQueryBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 获取学习历史\r\n"}, {"name": "savePlayRecord", "paramTypes": ["java.lang.Long"], "doc": "\n 保存视频播放记录\r\n\r\n @param videoId 视频ID\r\n"}, {"name": "getRelatedVideos", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": "\n 获取相关推荐视频\r\n\r\n @param id    视频ID\r\n @param limit 返回数量限制\r\n"}, {"name": "toggleLike", "paramTypes": ["java.lang.Long", "java.lang.Bo<PERSON>an"], "doc": "\n 切换视频点赞状态\r\n"}, {"name": "toggleCollect", "paramTypes": ["java.lang.Long", "java.lang.Bo<PERSON>an"], "doc": "\n 切换视频收藏状态\r\n"}, {"name": "shareVideo", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 分享视频\r\n"}, {"name": "incrementView", "paramTypes": ["java.lang.Long"], "doc": "\n 增加视频播放次数\r\n"}, {"name": "updateProgress", "paramTypes": ["java.lang.Long", "java.lang.Integer", "java.lang.Integer"], "doc": "\n 更新视频播放进度\r\n"}, {"name": "getPlayRecord", "paramTypes": ["java.lang.Long"], "doc": "\n 获取视频播放记录\r\n"}, {"name": "checkPurchaseStatus", "paramTypes": ["java.lang.Long"], "doc": "\n 检查视频购买状态\r\n"}, {"name": "to<PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.Long", "java.lang.Bo<PERSON>an"], "doc": "\n 关注/取关讲师\r\n"}, {"name": "getComments", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.bo.CommentQueryBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 获取视频评论列表\r\n"}, {"name": "publishComment", "paramTypes": ["org.dromara.app.domain.bo.VideoCommentBo"], "doc": "\n 发布视频评论\r\n"}, {"name": "toggleCommentLike", "paramTypes": ["java.lang.Long", "java.lang.Bo<PERSON>an"], "doc": "\n 切换评论点赞状态\r\n"}, {"name": "uploadVideo", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": "\n 上传视频文件\r\n"}, {"name": "uploadThumbnail", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": "\n 上传视频缩略图\r\n"}, {"name": "isVideoFile", "paramTypes": ["java.lang.String"], "doc": "\n 检查是否为视频文件\r\n"}, {"name": "isImageFile", "paramTypes": ["java.lang.String"], "doc": "\n 检查是否为图片文件\r\n"}], "constructors": []}