{"doc": "\n 向量嵌入实体\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "id", "doc": "\n 向量ID\r\n"}, {"name": "knowledgeBaseId", "doc": "\n 知识库ID\r\n"}, {"name": "documentId", "doc": "\n 文档ID\r\n"}, {"name": "content", "doc": "\n 文本块内容\r\n"}, {"name": "embedding", "doc": "\n 向量数据\r\n"}, {"name": "title", "doc": "\n 文本块标题\r\n"}, {"name": "summary", "doc": "\n 文本块摘要\r\n"}, {"name": "position", "doc": "\n 文本块在文档中的位置\r\n"}, {"name": "contentLength", "doc": "\n 文本块长度\r\n"}, {"name": "chunkType", "doc": "\n 文本块类型 (paragraph/heading/table/list/etc.)\r\n"}, {"name": "modelName", "doc": "\n 向量模型名称\r\n"}, {"name": "dimension", "doc": "\n 向量维度\r\n"}, {"name": "metadata", "doc": "\n 文本块元数据 (JSON格式)\r\n"}, {"name": "tags", "doc": "\n 文本块标签 (JSON数组)\r\n"}, {"name": "similarity", "doc": "\n 相似度分数 (查询时使用)\r\n"}, {"name": "sortOrder", "doc": "\n 排序字段\r\n"}, {"name": "remark", "doc": "\n 备注\r\n"}], "enumConstants": [], "methods": [], "constructors": []}