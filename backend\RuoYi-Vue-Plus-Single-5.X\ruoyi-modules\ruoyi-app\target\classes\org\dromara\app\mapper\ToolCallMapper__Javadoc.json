{"doc": "\n 工具调用Mapper接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectCallHistory", "paramTypes": ["com.baomidou.mybatisplus.extension.plugins.pagination.Page", "java.lang.String", "java.lang.Long"], "doc": "\n 分页查询工具调用历史\r\n\r\n @param page      分页对象\r\n @param sessionId 会话ID（可选）\r\n @param userId    用户ID\r\n @return 调用历史分页结果\r\n"}, {"name": "getUserToolStats", "paramTypes": ["java.lang.Long"], "doc": "\n 查询用户的工具调用统计\r\n\r\n @param userId 用户ID\r\n @return 统计信息\r\n"}, {"name": "getToolUsageStats", "paramTypes": ["java.lang.String"], "doc": "\n 查询工具的使用统计\r\n\r\n @param toolId 工具ID\r\n @return 统计信息\r\n"}, {"name": "getPopularTools", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询热门工具（按调用次数排序）\r\n\r\n @param limit 返回数量限制\r\n @return 热门工具列表\r\n"}, {"name": "getRecentCalls", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": "\n 查询用户最近的工具调用\r\n\r\n @param userId 用户ID\r\n @param limit  返回数量限制\r\n @return 最近调用列表\r\n"}, {"name": "getSessionToolCalls", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": "\n 查询会话中的工具调用\r\n\r\n @param sessionId 会话ID\r\n @param userId    用户ID\r\n @return 工具调用列表\r\n"}, {"name": "getErrorStats", "paramTypes": [], "doc": "\n 统计工具调用错误类型\r\n\r\n @return 错误统计\r\n"}, {"name": "getSlowCalls", "paramTypes": ["java.lang.Long"], "doc": "\n 查询执行时间超过阈值的工具调用\r\n\r\n @param threshold 时间阈值（毫秒）\r\n @return 慢调用列表\r\n"}, {"name": "batchInsertToolCalls", "paramTypes": ["java.util.List"], "doc": "\n 批量插入工具调用记录\r\n\r\n @param toolCalls 调用记录列表\r\n @return 影响行数\r\n"}], "constructors": []}