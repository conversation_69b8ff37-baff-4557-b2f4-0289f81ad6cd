{"doc": "\n 向量嵌入Mapper接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "searchSimilarVectors", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.Integer", "java.lang.Double"], "doc": "\n 向量相似度搜索\r\n\r\n @param knowledgeBaseId 知识库ID\r\n @param queryVector 查询向量\r\n @param limit 返回数量限制\r\n @param threshold 相似度阈值\r\n @return 相似文档列表\r\n"}, {"name": "hybridSearch", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.String", "java.lang.Integer", "java.lang.Double"], "doc": "\n 混合搜索（向量相似度 + 文本匹配）\r\n\r\n @param knowledgeBaseId 知识库ID\r\n @param queryVector 查询向量\r\n @param keywords 关键词\r\n @param limit 返回数量限制\r\n @param threshold 相似度阈值\r\n @return 相似文档列表\r\n"}, {"name": "selectByDocumentId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据文档ID查询向量\r\n\r\n @param documentId 文档ID\r\n @return 向量列表\r\n"}, {"name": "selectByKnowledgeBaseId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据知识库ID查询向量\r\n\r\n @param knowledgeBaseId 知识库ID\r\n @return 向量列表\r\n"}, {"name": "countByKnowledgeBaseId", "paramTypes": ["java.lang.Long"], "doc": "\n 统计知识库向量数量\r\n\r\n @param knowledgeBaseId 知识库ID\r\n @return 向量数量\r\n"}, {"name": "countByDocumentId", "paramTypes": ["java.lang.Long"], "doc": "\n 统计文档向量数量\r\n\r\n @param documentId 文档ID\r\n @return 向量数量\r\n"}, {"name": "deleteByDocumentId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据文档ID删除向量\r\n\r\n @param documentId 文档ID\r\n @return 删除数量\r\n"}, {"name": "deleteByKnowledgeBaseId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据知识库ID删除向量\r\n\r\n @param knowledgeBaseId 知识库ID\r\n @return 删除数量\r\n"}, {"name": "batchInsert", "paramTypes": ["java.util.List"], "doc": "\n 批量插入向量\r\n\r\n @param embeddings 向量列表\r\n @return 插入数量\r\n"}, {"name": "selectByChunkType", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 根据文本块类型查询向量\r\n\r\n @param knowledgeBaseId 知识库ID\r\n @param chunkType 文本块类型\r\n @return 向量列表\r\n"}], "constructors": []}