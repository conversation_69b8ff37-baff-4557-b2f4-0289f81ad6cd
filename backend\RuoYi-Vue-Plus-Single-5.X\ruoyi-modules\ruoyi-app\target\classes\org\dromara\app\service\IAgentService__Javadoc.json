{"doc": "\n AI代理服务接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getEnabledAgents", "paramTypes": [], "doc": "\n 获取所有启用的代理列表\r\n\r\n @return 代理列表\r\n"}, {"name": "getAgentByType", "paramTypes": ["java.lang.String"], "doc": "\n 根据类型获取代理\r\n\r\n @param agentType 代理类型\r\n @return 代理信息\r\n"}, {"name": "incrementUsageCount", "paramTypes": ["java.lang.String"], "doc": "\n 增加代理使用次数\r\n\r\n @param agentType 代理类型\r\n @return 是否成功\r\n"}, {"name": "updateRating", "paramTypes": ["java.lang.String", "java.lang.Double"], "doc": "\n 更新代理评分\r\n\r\n @param agentType 代理类型\r\n @param rating    评分\r\n @return 是否成功\r\n"}, {"name": "getQuickActions", "paramTypes": ["java.lang.String"], "doc": "\n 获取代理的快速操作列表\r\n\r\n @param agentType 代理类型\r\n @return 快速操作列表\r\n"}, {"name": "initDefaultAgents", "paramTypes": [], "doc": "\n 初始化默认代理数据\r\n"}, {"name": "enhanceQueryWithRAG", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 使用RAG增强用户查询\r\n\r\n @param agentType 代理类型\r\n @param userQuery 用户查询\r\n @return 增强后的查询内容\r\n"}, {"name": "getAgentKnowledgeBaseIds", "paramTypes": ["java.lang.String"], "doc": "\n 获取代理相关的知识库ID列表\r\n\r\n @param agentType 代理类型\r\n @return 知识库ID列表\r\n"}], "constructors": []}