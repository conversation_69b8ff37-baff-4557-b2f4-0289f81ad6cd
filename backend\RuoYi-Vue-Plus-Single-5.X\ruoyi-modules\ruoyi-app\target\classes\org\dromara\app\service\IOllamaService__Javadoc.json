{"doc": "\n Ollama服务接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "chat", "paramTypes": ["java.lang.String", "java.util.List", "java.lang.Double", "java.lang.Integer"], "doc": "\n 发送聊天消息（同步）\r\n\r\n @param model        模型名称\r\n @param messages     消息列表\r\n @param temperature  温度参数\r\n @param maxTokens    最大token数\r\n @return 响应结果\r\n"}, {"name": "chatStream", "paramTypes": ["java.lang.String", "java.util.List", "java.lang.Double", "java.lang.Integer", "org.springframework.web.servlet.mvc.method.annotation.SseEmitter"], "doc": "\n 发送聊天消息（流式）\r\n\r\n @param model        模型名称\r\n @param messages     消息列表\r\n @param temperature  温度参数\r\n @param maxTokens    最大token数\r\n @param sseEmitter   SSE发射器\r\n"}, {"name": "getAvailableModels", "paramTypes": [], "doc": "\n 获取可用模型列表\r\n\r\n @return 模型列表\r\n"}, {"name": "isModelAvailable", "paramTypes": ["java.lang.String"], "doc": "\n 检查模型是否可用\r\n\r\n @param modelName 模型名称\r\n @return 是否可用\r\n"}, {"name": "getModelInfo", "paramTypes": ["java.lang.String"], "doc": "\n 获取模型信息\r\n\r\n @param modelName 模型名称\r\n @return 模型信息\r\n"}, {"name": "checkServiceStatus", "paramTypes": [], "doc": "\n 检查Ollama服务状态\r\n\r\n @return 服务状态\r\n"}], "constructors": []}