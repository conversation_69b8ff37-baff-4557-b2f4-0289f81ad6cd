{"doc": "\n RAG知识库服务接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "createKnowledgeBase", "paramTypes": ["org.dromara.app.domain.KnowledgeBase"], "doc": "\n 创建知识库\r\n\r\n @param knowledgeBase 知识库信息\r\n @return 创建结果\r\n"}, {"name": "addDocument", "paramTypes": ["org.dromara.app.domain.KnowledgeDocument"], "doc": "\n 添加文档到知识库\r\n\r\n @param document 文档信息\r\n @return 添加结果\r\n"}, {"name": "processDocument", "paramTypes": ["java.lang.Long"], "doc": "\n 处理文档（分块、向量化）\r\n\r\n @param documentId 文档ID\r\n @return 处理结果\r\n"}, {"name": "searchKnowledge", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.Integer"], "doc": "\n 搜索相关知识\r\n\r\n @param knowledgeBaseId 知识库ID\r\n @param query 查询文本\r\n @param topK 返回数量\r\n @return 相关文档列表\r\n"}, {"name": "hybridSearch", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.Integer"], "doc": "\n 混合搜索（向量 + 关键词）\r\n\r\n @param knowledgeBaseId 知识库ID\r\n @param query 查询文本\r\n @param topK 返回数量\r\n @return 相关文档列表\r\n"}, {"name": "getKnowledgeBases", "paramTypes": [], "doc": "\n 获取知识库列表\r\n\r\n @return 知识库列表\r\n"}, {"name": "getKnowledgeBase", "paramTypes": ["java.lang.Long"], "doc": "\n 获取知识库详情\r\n\r\n @param knowledgeBaseId 知识库ID\r\n @return 知识库信息\r\n"}, {"name": "getDocuments", "paramTypes": ["java.lang.Long"], "doc": "\n 获取知识库文档列表\r\n\r\n @param knowledgeBaseId 知识库ID\r\n @return 文档列表\r\n"}, {"name": "deleteDocument", "paramTypes": ["java.lang.Long"], "doc": "\n 删除文档\r\n\r\n @param documentId 文档ID\r\n @return 删除结果\r\n"}, {"name": "deleteKnowledgeBase", "paramTypes": ["java.lang.Long"], "doc": "\n 删除知识库\r\n\r\n @param knowledgeBaseId 知识库ID\r\n @return 删除结果\r\n"}, {"name": "updateKnowledgeBase", "paramTypes": ["org.dromara.app.domain.KnowledgeBase"], "doc": "\n 更新知识库\r\n\r\n @param knowledgeBase 知识库信息\r\n @return 更新结果\r\n"}, {"name": "rebuildIndex", "paramTypes": ["java.lang.Long"], "doc": "\n 重建知识库索引\r\n\r\n @param knowledgeBaseId 知识库ID\r\n @return 重建结果\r\n"}, {"name": "getKnowledgeBaseStats", "paramTypes": ["java.lang.Long"], "doc": "\n 获取知识库统计信息\r\n\r\n @param knowledgeBaseId 知识库ID\r\n @return 统计信息\r\n"}], "constructors": []}