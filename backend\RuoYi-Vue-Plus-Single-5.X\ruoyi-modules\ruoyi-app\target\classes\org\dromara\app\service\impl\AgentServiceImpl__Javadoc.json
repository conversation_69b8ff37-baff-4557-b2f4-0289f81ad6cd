{"doc": "\n AI代理服务实现类\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "processAgentData", "paramTypes": ["org.dromara.app.domain.Agent"], "doc": "\n 处理Agent数据，解析JSON字段\r\n"}, {"name": "createDefaultAgents", "paramTypes": [], "doc": "\n 创建默认代理列表\r\n"}, {"name": "createAgent", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String", "java.util.List", "java.util.List", "java.lang.Integer"], "doc": "\n 创建Agent对象\r\n"}, {"name": "createDefaultModelConfig", "paramTypes": [], "doc": "\n 创建默认模型配置\r\n"}, {"name": "getKnowledgeBaseIdsByType", "paramTypes": ["java.lang.String"], "doc": "\n 根据类型获取知识库ID列表\r\n"}], "constructors": []}