{"doc": "\n 基于LangChain4j的聊天服务实现类\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "callAgentService", "paramTypes": ["java.lang.String", "java.lang.String", "java.util.Map", "java.lang.Long"], "doc": "\n 调用特定类型的Agent服务（同步）\r\n\r\n @param agentType Agent类型\r\n @param message 用户消息\r\n @param params 额外参数\r\n @param userId 用户ID\r\n @return 处理结果\r\n"}, {"name": "callAgentServiceStream", "paramTypes": ["java.lang.String", "java.lang.String", "java.util.Map", "java.lang.Long"], "doc": "\n 调用特定类型的Agent服务（流式）\r\n\r\n @param agentType Agent类型\r\n @param message 用户消息\r\n @param params 额外参数\r\n @param userId 用户ID\r\n @return SSE流\r\n"}, {"name": "invokeAgentService", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 根据Agent类型调用相应的服务\r\n"}, {"name": "invokeStreamingAgentService", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String", "java.lang.String", "java.lang.String", "dev.langchain4j.model.chat.response.StreamingChatResponseHandler"], "doc": "\n 调用流式Agent服务\r\n"}, {"name": "createAgentContext", "paramTypes": ["java.lang.String", "java.lang.Long", "java.lang.String"], "doc": "\n 创建Agent上下文\r\n"}, {"name": "getOrCreateMemory", "paramTypes": ["java.lang.String"], "doc": "\n 获取或创建内存\r\n"}, {"name": "loadHistoryToMemory", "paramTypes": ["java.lang.String", "dev.langchain4j.memory.chat.MessageWindowChatMemory"], "doc": "\n 从数据库加载历史消息到内存\r\n"}, {"name": "enhanceMemoryWithSystemPrompt", "paramTypes": ["dev.langchain4j.memory.chat.MessageWindowChatMemory", "java.lang.String"], "doc": "\n 根据Agent类型增强内存的系统提示词\r\n"}, {"name": "enhanceMessageWithRAG", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 使用RAG增强消息\r\n"}, {"name": "buildMetadata", "paramTypes": ["org.dromara.app.domain.dto.ChatRequestDto", "org.dromara.app.domain.Agent"], "doc": "\n 构建消息元数据\r\n"}], "constructors": []}