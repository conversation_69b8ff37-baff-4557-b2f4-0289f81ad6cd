{"doc": "\n RAG知识库服务实现类\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "processDocumentAsync", "paramTypes": ["java.lang.Long"], "doc": "\n 异步处理文档\r\n"}, {"name": "chunkDocument", "paramTypes": ["java.lang.String"], "doc": "\n 文档分块\r\n"}, {"name": "updateKnowledgeBaseVectorCount", "paramTypes": ["java.lang.Long"], "doc": "\n 更新知识库向量数量\r\n"}, {"name": "updateKnowledgeBaseStats", "paramTypes": ["java.lang.Long"], "doc": "\n 更新知识库统计信息\r\n"}, {"name": "vectorArrayToString", "paramTypes": ["float[]"], "doc": "\n 向量数组转换为字符串\r\n"}], "constructors": []}