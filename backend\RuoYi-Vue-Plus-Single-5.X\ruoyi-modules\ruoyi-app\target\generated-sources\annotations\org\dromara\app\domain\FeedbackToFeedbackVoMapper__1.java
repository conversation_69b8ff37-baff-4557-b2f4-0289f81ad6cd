package org.dromara.app.domain;

import io.github.linpeilie.AutoMapperConfig__17;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.bo.FeedbackBoToFeedbackMapper__1;
import org.dromara.app.domain.vo.FeedbackVo;
import org.dromara.app.domain.vo.FeedbackVoToFeedbackMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__17.class,
    uses = {FeedbackVoToFeedbackMapper__1.class,FeedbackBoToFeedbackMapper__1.class},
    imports = {}
)
public interface FeedbackToFeedbackVoMapper__1 extends BaseMapper<Feedback, FeedbackVo> {
}
