package org.dromara.app.domain;

import io.github.linpeilie.AutoMapperConfig__17;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.bo.VideoCommentBoToVideoCommentMapper__1;
import org.dromara.app.domain.vo.VideoCommentVo;
import org.dromara.app.domain.vo.VideoCommentVoToVideoCommentMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__17.class,
    uses = {VideoCommentBoToVideoCommentMapper__1.class,VideoCommentVoToVideoCommentMapper__1.class},
    imports = {}
)
public interface VideoCommentToVideoCommentVoMapper__1 extends BaseMapper<VideoComment, VideoCommentVo> {
}
