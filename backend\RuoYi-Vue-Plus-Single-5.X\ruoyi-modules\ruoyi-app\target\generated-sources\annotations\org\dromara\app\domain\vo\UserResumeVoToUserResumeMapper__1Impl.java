package org.dromara.app.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.app.domain.UserResume;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-16T17:50:43+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class UserResumeVoToUserResumeMapper__1Impl implements UserResumeVoToUserResumeMapper__1 {

    @Override
    public UserResume convert(UserResumeVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserResume userResume = new UserResume();

        userResume.setCreateBy( arg0.getCreateBy() );
        userResume.setCreateTime( arg0.getCreateTime() );
        userResume.setUpdateTime( arg0.getUpdateTime() );
        userResume.setFilePath( arg0.getFilePath() );
        userResume.setFileSize( arg0.getFileSize() );
        userResume.setFileSuffix( arg0.getFileSuffix() );
        userResume.setFileUrl( arg0.getFileUrl() );
        userResume.setIsDefault( arg0.getIsDefault() );
        userResume.setOriginalName( arg0.getOriginalName() );
        userResume.setOssId( arg0.getOssId() );
        userResume.setRemark( arg0.getRemark() );
        userResume.setResumeId( arg0.getResumeId() );
        userResume.setResumeName( arg0.getResumeName() );
        userResume.setStatus( arg0.getStatus() );
        userResume.setUserId( arg0.getUserId() );

        return userResume;
    }

    @Override
    public UserResume convert(UserResumeVo arg0, UserResume arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setFilePath( arg0.getFilePath() );
        arg1.setFileSize( arg0.getFileSize() );
        arg1.setFileSuffix( arg0.getFileSuffix() );
        arg1.setFileUrl( arg0.getFileUrl() );
        arg1.setIsDefault( arg0.getIsDefault() );
        arg1.setOriginalName( arg0.getOriginalName() );
        arg1.setOssId( arg0.getOssId() );
        arg1.setRemark( arg0.getRemark() );
        arg1.setResumeId( arg0.getResumeId() );
        arg1.setResumeName( arg0.getResumeName() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setUserId( arg0.getUserId() );

        return arg1;
    }
}
