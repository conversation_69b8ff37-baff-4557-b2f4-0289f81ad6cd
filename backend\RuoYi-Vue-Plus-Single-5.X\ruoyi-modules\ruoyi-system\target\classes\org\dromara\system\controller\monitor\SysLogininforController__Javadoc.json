{"doc": "\n 系统访问记录\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.system.domain.bo.SysLogininforBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 获取系统访问记录列表\r\n"}, {"name": "export", "paramTypes": ["org.dromara.system.domain.bo.SysLogininforBo", "jakarta.servlet.http.HttpServletResponse"], "doc": "\n 导出系统访问记录列表\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": "\n 批量删除登录日志\r\n @param infoIds 日志ids\r\n"}, {"name": "clean", "paramTypes": [], "doc": "\n 清理系统访问记录\r\n"}], "constructors": []}