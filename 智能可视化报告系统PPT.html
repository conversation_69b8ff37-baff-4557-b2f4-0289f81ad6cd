<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能可视化报告系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
            min-height: 100vh;
            padding: 15px;
            margin: 0;
        }

        .container {
            max-width: 1600px;
            width: 100%;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(46, 139, 87, 0.1);
            overflow: hidden;
            min-height: calc(100vh - 30px);
        }

        .header {
            background: linear-gradient(135deg, #2E8B57 0%, #3CB371 100%);
            color: white;
            text-align: center;
            padding: 25px 20px;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
        }

        .header h1 {
            font-size: 2.2em;
            font-weight: bold;
            margin-bottom: 8px;
            position: relative;
            z-index: 1;
        }

        .header .subtitle {
            font-size: 1.1em;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            gap: 20px;
            padding: 25px;
        }

        .section {
            background: #f8fffe;
            border-radius: 12px;
            padding: 20px;
            border: 2px solid #e0f2e0;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            height: fit-content;
        }

        .section:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(46, 139, 87, 0.15);
        }

        .section-header {
            display: flex;
            align-items: center;
            margin-bottom: 18px;
        }

        .section-number {
            background: linear-gradient(135deg, #2E8B57, #3CB371);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.3em;
            font-weight: bold;
            margin-right: 12px;
        }

        .section-title {
            font-size: 1.2em;
            color: #2E8B57;
            font-weight: bold;
        }

        .feature-list {
            list-style: none;
        }

        .feature-item {
            background: white;
            margin-bottom: 8px;
            padding: 12px;
            border-radius: 8px;
            border-left: 4px solid #90EE90;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
            font-size: 0.9em;
        }

        .feature-item:hover {
            border-left-color: #2E8B57;
            transform: translateX(5px);
        }

        .feature-item::before {
            content: '✓';
            color: #2E8B57;
            font-weight: bold;
            margin-right: 10px;
        }

        .bottom-section {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
            gap: 15px;
            padding: 0 25px 20px;
        }

        .additional-section {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
            padding: 0 25px 25px;
        }

        .stats-section {
            background: linear-gradient(135deg, #ffffff 0%, #f8fff8 100%);
            border-radius: 15px;
            padding: 25px;
            border: 2px solid #e0f2e0;
            text-align: center;
            transition: transform 0.3s ease;
        }

        .stats-section:hover {
            transform: translateY(-3px);
        }

        .stats-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #2E8B57;
            margin-bottom: 10px;
        }

        .stats-label {
            color: #2d5a2d;
            font-size: 1.1em;
            font-weight: 500;
        }

        .feature-detail {
            font-size: 0.8em;
            color: #666;
            margin-top: 4px;
            font-style: italic;
            line-height: 1.3;
        }

        .insight-section {
            background: linear-gradient(135deg, #f0f8f0 0%, #e8f5e8 100%);
            border-radius: 15px;
            padding: 25px;
            border: 2px solid #d0e8d0;
        }

        .insight-title {
            color: #2E8B57;
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .insight-title::before {
            content: '📊';
            margin-right: 10px;
        }

        .tech-title::before {
            content: '🔧';
        }

        .insight-list {
            list-style: none;
        }

        .insight-item {
            padding: 8px 0;
            color: #2d5a2d;
            border-bottom: 1px solid #e0f0e0;
        }

        .insight-item:last-child {
            border-bottom: none;
        }

        .insight-item::before {
            content: '▶';
            color: #2E8B57;
            margin-right: 8px;
            font-size: 0.8em;
        }

        @media (max-width: 768px) {
            .main-content,
            .bottom-section,
            .additional-section {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .header h1 {
                font-size: 2em;
            }

            .container {
                margin: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>模块六：智能可视化报告系统</h1>
            <div class="subtitle">数据驱动决策，智能洞察未来</div>
        </div>

        <div class="main-content">
            <div class="section">
                <div class="section-header">
                    <div class="section-number">01</div>
                    <div class="section-title">个人能力报告</div>
                </div>
                <ul class="feature-list">
                    <li class="feature-item">
                        多维度能力雷达图，直观展示优势和短板
                        <div class="feature-detail">涵盖技术能力、沟通能力、领导力、创新思维等8大维度</div>
                    </li>
                    <li class="feature-item">
                        历史成长曲线，追踪能力提升轨迹
                        <div class="feature-detail">支持月度、季度、年度多时间维度分析</div>
                    </li>
                    <li class="feature-item">
                        个性化改进建议，提供具体提升方案
                        <div class="feature-detail">基于AI算法生成针对性学习路径和资源推荐</div>
                    </li>
                    <li class="feature-item">
                        能力认证与徽章系统
                        <div class="feature-detail">完成技能认证获得数字徽章，激励持续学习</div>
                    </li>
                    <li class="feature-item">
                        同行对比分析
                        <div class="feature-detail">与同岗位、同级别人员进行匿名化能力对比</div>
                    </li>
                </ul>
            </div>

            <div class="section">
                <div class="section-header">
                    <div class="section-number">02</div>
                    <div class="section-title">企业招聘分析</div>
                </div>
                <ul class="feature-list">
                    <li class="feature-item">
                        候选人对比分析，支持多人横向比较
                        <div class="feature-detail">一键生成候选人能力对比表，支持最多10人同时比较</div>
                    </li>
                    <li class="feature-item">
                        岗位匹配度排序，智能推荐最佳人选
                        <div class="feature-detail">基于岗位要求和候选人能力进行AI智能匹配评分</div>
                    </li>
                    <li class="feature-item">
                        招聘效果统计，分析招聘ROI和成功率
                        <div class="feature-detail">追踪从简历筛选到入职转正的全流程数据</div>
                    </li>
                    <li class="feature-item">
                        面试评估标准化
                        <div class="feature-detail">提供结构化面试模板和评分标准，减少主观偏差</div>
                    </li>
                    <li class="feature-item">
                        人才库智能管理
                        <div class="feature-detail">自动标签分类，快速检索历史候选人信息</div>
                    </li>
                </ul>
            </div>
        </div>

        <div class="bottom-section">
            <div class="insight-section">
                <div class="insight-title">行业趋势洞察</div>
                <ul class="insight-list">
                    <li class="insight-item">人才市场趋势分析，预测未来需求</li>
                    <li class="insight-item">薪资水平对标，提供市场参考</li>
                    <li class="insight-item">技能热度排行，指导培训方向</li>
                    <li class="insight-item">竞争对手招聘策略分析</li>
                    <li class="insight-item">行业人才流动性监测</li>
                </ul>
            </div>

            <div class="insight-section">
                <div class="insight-title tech-title">可视化技术</div>
                <ul class="insight-list">
                    <li class="insight-item">基于ECharts的丰富图表类型</li>
                    <li class="insight-item">交互式数据探索，支持钻取和筛选</li>
                    <li class="insight-item">响应式设计，适配不同设备</li>
                    <li class="insight-item">实时数据更新与动态展示</li>
                    <li class="insight-item">支持PDF/Excel多格式导出</li>
                </ul>
            </div>
        </div>

        <div class="main-content">
            <div class="section">
                <div class="section-header">
                    <div class="section-number">03</div>
                    <div class="section-title">智能决策支持</div>
                </div>
                <ul class="feature-list">
                    <li class="feature-item">
                        AI驱动的人才推荐引擎
                        <div class="feature-detail">基于深度学习算法，精准匹配人才与岗位</div>
                    </li>
                    <li class="feature-item">
                        风险预警系统
                        <div class="feature-detail">提前识别人才流失风险，制定挽留策略</div>
                    </li>
                    <li class="feature-item">
                        培训需求智能分析
                        <div class="feature-detail">根据能力缺口自动生成个性化培训计划</div>
                    </li>
                    <li class="feature-item">
                        绩效预测模型
                        <div class="feature-detail">基于历史数据预测员工未来绩效表现</div>
                    </li>
                </ul>
            </div>

            <div class="section">
                <div class="section-header">
                    <div class="section-number">04</div>
                    <div class="section-title">数据安全与合规</div>
                </div>
                <ul class="feature-list">
                    <li class="feature-item">
                        多层级权限管理
                        <div class="feature-detail">细粒度权限控制，确保数据访问安全</div>
                    </li>
                    <li class="feature-item">
                        数据加密存储
                        <div class="feature-detail">采用AES-256加密，保护敏感信息</div>
                    </li>
                    <li class="feature-item">
                        审计日志追踪
                        <div class="feature-detail">完整记录数据访问和操作轨迹</div>
                    </li>
                    <li class="feature-item">
                        GDPR合规支持
                        <div class="feature-detail">支持数据删除权、可携带权等合规要求</div>
                    </li>
                </ul>
            </div>
        </div>

        <div class="additional-section">
            <div class="stats-section">
                <div class="stats-number">95%</div>
                <div class="stats-label">预测准确率</div>
            </div>
            <div class="stats-section">
                <div class="stats-number">50+</div>
                <div class="stats-label">图表类型</div>
            </div>
            <div class="stats-section">
                <div class="stats-number">24/7</div>
                <div class="stats-label">实时监控</div>
            </div>
        </div>

        <div class="bottom-section">
            <div class="insight-section">
                <div class="insight-title">🔗 系统集成能力</div>
                <ul class="insight-list">
                    <li class="insight-item">支持主流HR系统API对接</li>
                    <li class="insight-item">与钉钉、企业微信无缝集成</li>
                    <li class="insight-item">兼容SAP、Oracle HCM等企业级系统</li>
                    <li class="insight-item">支持自定义数据源接入</li>
                    <li class="insight-item">提供标准RESTful API接口</li>
                </ul>
            </div>

            <div class="insight-section">
                <div class="insight-title">⚡ 技术架构优势</div>
                <ul class="insight-list">
                    <li class="insight-item">微服务架构，支持弹性扩展</li>
                    <li class="insight-item">云原生部署，支持多云环境</li>
                    <li class="insight-item">大数据处理能力，支持PB级数据</li>
                    <li class="insight-item">机器学习模型持续优化</li>
                    <li class="insight-item">99.9%系统可用性保障</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
